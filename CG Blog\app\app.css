@import "tailwindcss";

@custom-variant dark (&:is(.dark *));
@plugin "tailwindcss-animate";

@theme {
  --font-sans: "<PERSON>", "<PERSON>", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(0 0% 3.9%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(0 0% 3.9%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 3.9%);
  --primary: hsl(0 0% 9%);
  --primary-foreground: hsl(0 0% 98%);
  --secondary: hsl(0 0% 96.1%);
  --secondary-foreground: hsl(0 0% 9%);
  --muted: hsl(0 0% 96.1%);
  --muted-foreground: hsl(0 0% 45.1%);
  --accent: hsl(0 0% 96.1%);
  --accent-foreground: hsl(0 0% 9%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(0 0% 89.8%);
  --input: hsl(0 0% 89.8%);
  --ring: hsl(0 0% 3.9%);
  --chart-1: hsl(12 76% 61%);
  --chart-2: hsl(173 58% 39%);
  --chart-3: hsl(197 37% 24%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --radius: 0.6rem;
}

[data-theme="dark"] {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(0 0% 98%);
  --card: hsl(214, 10%, 14%);
  --card-foreground: hsl(0 0% 98%);
  --popover: hsl(0 0% 3.9%);
  --popover-foreground: hsl(0 0% 98%);
  --primary: hsl(0 0% 98%);
  --primary-foreground: hsl(0 0% 9%);
  --secondary: hsl(0 0% 14.9%);
  --secondary-foreground: hsl(0 0% 98%);
  --muted: hsl(0 0% 14.9%);
  --muted-foreground: hsl(0 0% 63.9%);
  --accent: hsl(0 0% 14.9%);
  --accent-foreground: hsl(0 0% 98%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(0 0% 98%);
  --border: hsl(0 0% 14.9%);
  --input: hsl(0 0% 14.9%);
  --ring: hsl(0 0% 83.1%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  .prose {
    /* Headers */
    --tw-prose-headings: theme("colors.foreground");
    --tw-prose-links: theme("colors.blue.600");

    /* Body text */
    --tw-prose-body: theme("colors.foreground/80");
    --tw-prose-bold: theme("colors.foreground");

    /* Code blocks */
    --tw-prose-code: theme("colors.foreground");
    --tw-prose-pre-code: theme("colors.foreground");
    --tw-prose-pre-bg: var(--muted);

    /* Quotes */
    --tw-prose-quotes: theme("colors.foreground/80");
    --tw-prose-quote-borders: theme("colors.border");

    /* Lists */
    --tw-prose-bullets: theme("colors.foreground/60");

    /* Dark mode */
    --tw-prose-invert-body: theme("colors.foreground/80");
    --tw-prose-invert-headings: theme("colors.foreground");
    --tw-prose-invert-links: theme("colors.blue.400");
    --tw-prose-invert-bold: theme("colors.foreground");
    --tw-prose-invert-quotes: theme("colors.foreground/80");
  }

  /* Code block styling */
  .prose pre {
    @apply p-4 rounded-lg bg-muted overflow-x-auto;
  }

  /* Inline code styling */
  .prose code {
    @apply px-1 py-0.5 rounded-md bg-muted text-foreground;
  }

  /* Table styling */
  .prose table {
    @apply w-full border-collapse border border-border;
  }

  .prose th,
  .prose td {
    @apply border border-border p-2;
  }

  .prose thead {
    @apply bg-muted;
  }

  /* List styling */
  .prose ul,
  .prose ol {
    @apply my-6 space-y-2;
  }

  /* Blockquote styling */
  .prose blockquote {
    @apply pl-4 border-l-4 border-border italic;
  }
}

.prose h2 {
  @apply text-2xl font-bold mt-8 mb-4 text-foreground border-b pb-2 border-border;
  scroll-margin-top: 4.5rem;
}

.prose h3 {
  @apply text-xl font-semibold mt-6 mb-3 text-foreground/90;
  scroll-margin-top: 4.5rem;
}

.prose p {
  @apply text-foreground/80 leading-relaxed mb-4;
}

.prose ul {
  @apply my-4 list-disc list-inside;
}

.prose li {
  @apply text-foreground/80 mb-2;
}

/* Fix for list items with paragraphs */
.prose li p:first-child {
  @apply mb-0 inline-block;
}

.prose li p {
  @apply mb-2;
}

.prose a {
  @apply text-blue-600 hover:underline dark:text-blue-400;
}

::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

::view-transition-old(root) {
  z-index: 1;
}
::view-transition-new(root) {
  z-index: 2;
}

.dark::view-transition-old(root) {
  z-index: 2;
}
.dark::view-transition-new(root) {
  z-index: 1;
}

* {
  transition: all 100ms ease;
}

/* Scrollbar styling for code blocks */
.scrollbar-themed::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.scrollbar-themed::-webkit-scrollbar-track {
  background: var(--secondary);
  border-radius: 4px;
}

.scrollbar-themed::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

.scrollbar-themed::-webkit-scrollbar-thumb:hover {
  background: var(--foreground/60);
}

/* Ensure code blocks with long lines don't overflow */
.prose pre {
  max-width: 100%;
  word-wrap: normal;
}

.prose pre code {
  white-space: pre;
  word-break: normal;
  word-wrap: normal;
}
