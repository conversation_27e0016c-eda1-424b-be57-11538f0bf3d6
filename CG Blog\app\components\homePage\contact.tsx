import { useEffect, useState } from "react";
import { BASE_URL } from "~/config/constants";
import { Button } from "~/components/ui/button";

interface ContactProps {
  loading?: boolean;
  title?: string;
  text?: string;
  buttonLink?: string;
}

export default function Contact({
  loading,
  title,
  text,
  buttonLink,
}: ContactProps) {
  if (loading) {
    return (
      <section className="py-16">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="text-center space-y-6">
            <div className="h-8 w-64 animate-pulse rounded-md bg-muted mx-auto" />
            <div className="h-4 w-96 animate-pulse rounded-md bg-muted mx-auto" />
            <div className="h-10 w-32 animate-pulse rounded-md bg-muted mx-auto" />
          </div>
        </div>
      </section>
    );
  }

  if (!title || !text || !buttonLink) return null;

  return (
    <section className="py-16">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="text-center space-y-6">
          <h2 className="text-3xl lg:text-4xl font-bold tracking-tighter">
            {title}
          </h2>
          <p className="text-lg lg:text-xl text-muted-foreground max-w-2xl mx-auto">
            {text}
          </p>
          <Button
            asChild
            size="lg"
            className="hover:scale-105 transition-transform duration-300"
          >
            <a href={buttonLink}>Contact Me</a>
          </Button>
        </div>
      </div>
    </section>
  );
}
