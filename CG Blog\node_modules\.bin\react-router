#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+dev@7.1.5_@re_362f264b86b49fd7c483f364ee12e5e5/node_modules/@react-router/dev/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+dev@7.1.5_@re_362f264b86b49fd7c483f364ee12e5e5/node_modules/@react-router/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+dev@7.1.5_@re_362f264b86b49fd7c483f364ee12e5e5/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+dev@7.1.5_@re_362f264b86b49fd7c483f364ee12e5e5/node_modules/@react-router/dev/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+dev@7.1.5_@re_362f264b86b49fd7c483f364ee12e5e5/node_modules/@react-router/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+dev@7.1.5_@re_362f264b86b49fd7c483f364ee12e5e5/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@react-router/dev/bin.js" "$@"
else
  exec node  "$basedir/../@react-router/dev/bin.js" "$@"
fi
