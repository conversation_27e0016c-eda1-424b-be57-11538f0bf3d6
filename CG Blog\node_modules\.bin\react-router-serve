#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911/node_modules/@react-router/serve/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911/node_modules/@react-router/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911/node_modules/@react-router/serve/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911/node_modules/@react-router/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@react-router/serve/bin.js" "$@"
else
  exec node  "$basedir/../@react-router/serve/bin.js" "$@"
fi
