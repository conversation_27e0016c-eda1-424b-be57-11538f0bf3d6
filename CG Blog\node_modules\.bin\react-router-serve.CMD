@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911\node_modules\@react-router\serve\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911\node_modules\@react-router\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911\node_modules\@react-router\serve\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911\node_modules\@react-router\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\@react-router+serve@7.1.5_r_0a039294b6037ef485c30d9168317911\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@react-router\serve\bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@react-router\serve\bin.js" %*
)
