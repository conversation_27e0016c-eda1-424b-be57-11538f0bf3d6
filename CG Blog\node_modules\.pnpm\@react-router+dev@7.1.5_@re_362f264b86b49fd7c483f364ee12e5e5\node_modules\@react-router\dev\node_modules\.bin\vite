#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/vite@5.4.14_@types+node@20.17.17_lightningcss@1.29.1/node_modules/vite/bin/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/vite@5.4.14_@types+node@20.17.17_lightningcss@1.29.1/node_modules/vite/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/vite@5.4.14_@types+node@20.17.17_lightningcss@1.29.1/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/vite@5.4.14_@types+node@20.17.17_lightningcss@1.29.1/node_modules/vite/bin/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/vite@5.4.14_@types+node@20.17.17_lightningcss@1.29.1/node_modules/vite/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/vite@5.4.14_@types+node@20.17.17_lightningcss@1.29.1/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../vite/bin/vite.js" "$@"
fi
