#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/beautify@0.0.8/node_modules/beautify/bin/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/beautify@0.0.8/node_modules/beautify/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/beautify@0.0.8/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/beautify@0.0.8/node_modules/beautify/bin/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/beautify@0.0.8/node_modules/beautify/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/beautify@0.0.8/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../beautify/bin/beautify.js" "$@"
else
  exec node  "$basedir/../beautify/bin/beautify.js" "$@"
fi
