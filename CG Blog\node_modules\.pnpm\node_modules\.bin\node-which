#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/which@3.0.1/node_modules/which/bin/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/which@3.0.1/node_modules/which/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/which@3.0.1/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/which@3.0.1/node_modules/which/bin/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/which@3.0.1/node_modules/which/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/which@3.0.1/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../which/bin/which.js" "$@"
else
  exec node  "$basedir/../which/bin/which.js" "$@"
fi
