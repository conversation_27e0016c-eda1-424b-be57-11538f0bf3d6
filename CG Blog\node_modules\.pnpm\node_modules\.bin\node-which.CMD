@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\which@3.0.1\node_modules\which\bin\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\which@3.0.1\node_modules\which\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\which@3.0.1\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\which@3.0.1\node_modules\which\bin\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\which@3.0.1\node_modules\which\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\which@3.0.1\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\which\bin\which.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\which\bin\which.js" %*
)
