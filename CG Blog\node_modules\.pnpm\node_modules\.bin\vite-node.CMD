@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\vite-node@3.0.0-beta.2_@typ_235d2dda3358501363bfdf168b54f0ca\node_modules\vite-node\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\vite-node@3.0.0-beta.2_@typ_235d2dda3358501363bfdf168b54f0ca\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\vite-node@3.0.0-beta.2_@typ_235d2dda3358501363bfdf168b54f0ca\node_modules\vite-node\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\vite-node@3.0.0-beta.2_@typ_235d2dda3358501363bfdf168b54f0ca\node_modules;M:\Others\CG-Blog\CG Blog\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vite-node\vite-node.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vite-node\vite-node.mjs" %*
)
