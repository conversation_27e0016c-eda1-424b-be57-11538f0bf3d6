#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/tsconfck@3.1.5_typescript@5.7.3/node_modules/tsconfck/bin/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/tsconfck@3.1.5_typescript@5.7.3/node_modules/tsconfck/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/tsconfck@3.1.5_typescript@5.7.3/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/tsconfck@3.1.5_typescript@5.7.3/node_modules/tsconfck/bin/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/tsconfck@3.1.5_typescript@5.7.3/node_modules/tsconfck/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/tsconfck@3.1.5_typescript@5.7.3/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../tsconfck/bin/tsconfck.js" "$@"
else
  exec node  "$basedir/../../../tsconfck/bin/tsconfck.js" "$@"
fi
