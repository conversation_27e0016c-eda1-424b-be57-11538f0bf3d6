#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/vite-node@3.0.0-beta.2_@typ_235d2dda3358501363bfdf168b54f0ca/node_modules/vite-node/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/vite-node@3.0.0-beta.2_@typ_235d2dda3358501363bfdf168b54f0ca/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/vite-node@3.0.0-beta.2_@typ_235d2dda3358501363bfdf168b54f0ca/node_modules/vite-node/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/vite-node@3.0.0-beta.2_@typ_235d2dda3358501363bfdf168b54f0ca/node_modules:/mnt/m/Others/CG-Blog/CG Blog/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../vite-node/vite-node.mjs" "$@"
else
  exec node  "$basedir/../../../../vite-node/vite-node.mjs" "$@"
fi
