{"name": "cg-blog", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev --host 0.0.0.0 --port 3000", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@react-router/node": "^7.1.5", "@react-router/serve": "^7.1.5", "@sindresorhus/slugify": "^2.2.1", "@types/lodash": "^4.17.15", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "framer-motion": "^12.4.7", "html2canvas": "^1.4.1", "isbot": "^5.1.17", "jspdf": "^3.0.1", "lucide-react": "^0.470.0", "motion": "^12.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.15.1", "react-markdown": "^9.0.3", "react-router": "^7.1.5", "react-router-dom": "7", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@react-router/dev": "^7.1.5", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.6", "@types/node": "^20.17.17", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "react-router-devtools": "^1.1.0", "tailwindcss": "^4.0.6", "typescript": "^5.7.2", "vite": "^5.4.11", "vite-tsconfig-paths": "^5.1.4"}, "packageManager": "pnpm@10.4.0+sha512.6b849d0787d97f8f4e1f03a9b8ff8f038e79e153d6f11ae539ae7c435ff9e796df6a862c991502695c7f9e8fac8aeafc1ac5a8dab47e36148d183832d886dd52", "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}