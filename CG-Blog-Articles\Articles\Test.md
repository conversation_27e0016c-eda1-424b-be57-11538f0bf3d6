# عرض النصوص بكل الخطوط على جهازك بسهولة Hello عرض

# المشكلة في المعاينة العادية:

صحيح الفوتوشوب بيديك معاينة، لكنها كلها بكلمة **"أبجد هوز"** أو **"Sample"**.

### مزايا البرنامج:

1. **اختيار الفونت المناسب بسهولة:**
   لما تلاقي الفونت اللي يعجبك، بمجرد الضغط على زر **كليك يمين**، تقدر تنسخ اسم الخط.

2. **معاينة فولدرات الخطوط:**
   لو عندك فولدر فيه خطوط إضافية، تقدر تعاينه كله، وتختار أي خط يعجبك لتثبيته مباشرة.

3. **برنامج مفتوح المصدر:**
   كالعادة، البرنامج مفتوح المصدر ومتاح لك لتحميله مجاناً!

Helloooooo

1. **f\*ck the test:**

---

### التحميل:

link to project:
[**https://github.com/Creative-Geek/Font-Previewer**](https://github.com/Creative-Geek/Font-Previewer)

# المشكلة في المعاينة العادية:

## Get Started in 4 Steps

1. Clone:

```bash
git clone https://github.com/Creative-Geek/cg-blog.git
```

2. Create Content:

   - Copy the template folder contents to a new folder and edit the content.
   - Set a build command:

     ```bash
     node generate-index.js
     ```

- Deploy to any static serving service.

1. Set content source in `src/constants.js`:

```js
// Use any raw URL that provides the files.
export const BASE_URL = "YOUR_MARKDOWN_FILES_URL";
export const NAME = "Creative Geek";
```

4. Launch:

```bash
npm install && npm run dev
```

---

- Test
- Test 2:

  - Test 2.1

    Hi There!

  - Test 2.2

- Test 3
  1. Test 3.1
     - 13131
  2. Test 3.2
