من 5 سنين عملت فيديو بعنوان "سحر ضغط الفيديوهات"، و لينك الفيديو هو:  
[https://youtu.be/kDbo4zsQEE0](https://youtu.be/kDbo4zsQEE0)  
الفيديو ده فضل ماسك معلومات حديثة لغاية السنة اللي فاتت، لكن دلوقتي حصل تطور كبير!

---

## AV1: التشفير الجديد المفتوح المصدر

تشفير AV1 اللي بدأ دعمه للستريمينج الأسبوع اللي فات في برنامج OBS Studio هو اللي المفروض تستخدمه دلوقتي بدل HEVC.  
الميزات زي أي تشفير تاني زي ما اتكلمت عنه في الفيديو:

- حجم قليل
- جودة أفضل  
  بس يفرق إنه مفتوح المصدر، مفيش شركة محتاجة تدفع فلوس عشان تستخدمه، على عكس HEVC اللي الشركات الكبيرة مش بتستخدمه بسبب مشاكل الحقوق دي.

---

## المشاكل وال Limitations

المشكلة الرئيسية إن AV1 محتاج جهاز أقوي بكتير للتشفير وفك التشفير.  
هذه المشكلة إتحلت عن طريق كروت الشاشة بالنسبة لـ HEVC:

- كل كروت انفيديا 1000 فيما فوق (بما فيهم الـ 1030) بيدعموا فك تشفير الـ HEVC بالهاردوير، مع وجود IC مخصوص في التشيباية وظيفتها بس إنها تفك التشفير ده.
- كروت معينة منهم بس اللي بتقدر تشفر الفيديوهات بالهاردوير، أقل واحد يقدر يشفر هو الـ 1050.

أما AV1، فحالياً متاح فك تشفيره بالهاردوير على كروت الـ RTX 3000 فيما فوق، وتشفيره بالهاردوير متاح على كروت RTX 4000 وكروت RX 7000 وكروت Intel Arc.  
أي جهاز تاني هيحتاج يستخدم البروسيسور، واللي أبطأ بكتير جداً من أي Hardware Acceleration، وطبعاً ده بيخليه ميستاهلش.

---

## تجربة عملية

في تجربة على فيديو lyrics كنت عامله زمان، جربت أشفره بالهاردوير بتاع انفيديا لـ HEVC و بالبروسيسور لـ AV1.  
النتيجة هي:

- **50% نقص في المساحة** لصالح AV1
- **جودة أفضل** بحاجة بسيطة جداً صعب تتلاحظ إلا لو عارف انت بتدور على ايه.

---

## الختام

جاهز لاستقبال أسئلةكم! (مفيش أسئلة غبية، فيه أجوبة غبية).
