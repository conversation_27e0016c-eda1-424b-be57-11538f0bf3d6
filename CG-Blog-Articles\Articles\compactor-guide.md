تعرف إنك تقدر تضغط الويندوز بتاعك وتوفر حوالي 10 جيجا من الـ C؟ طب تعرف كمان إنك تقدر تضغط أي ملف على جهازك وتوفر أحياناً لغاية 50% من المساحة؟

السلام عليكم، معاكم أحمد طه من Creative Geek. النهاردة مش جاي أقول جديد، أنا كتبت مقال قبل كدة بيوضح الـ File Compression وآلية عمله (link to be added).

الجديد هنا هو طريقة أحسن بكتير لاستخدام الخاصية دي. لكن قبل ما نبدأ، خلينا نعمل مراجعة سريعة للناس اللي ماقرتش المقال القديم:

## مراجعة سريعة عن ضغط الملفات

نظام الملفات NTFS بيدعم خاصية بتخليك تضغط الملفات بكذا خوارزمية. الضغط بيقلل حجم الملفات، وبيتقري وهو مضغوط، وبعدين البروسيسور يفك ضغطه في الرام. يعني بتشتري مساحة تخزين مقابل أداء البروسيسور. لكن في أغلب الأجهزة، الأداء بيتحسن لأن الهارد ديسك بيكون أحياناً أبطأ من البروسيسور.

ويندوز فيه برنامج صغير اسمه `compact.exe` بيشغل خاصية الضغط. تقدر تضغط الويندوز عن طريقه بالأمر ده (في cmd كمسؤول):

```bash
Compact.exe /CompactOS:always
```

هياخد شوية وقت، بس هيوفرلك حوالي 10 جيجا (ممكن أكتر أو أقل).

## برنامج Compactor: ضغط الملفات بذكاء

فيه برنامج مفتوح المصدر اسمه **Compactor** بيستخدم `compact.exe` بذكاء أكبر وبيسهل العملية. لينك البرنامج على GitHub: [https://github.com/Freaky/Compactor](https://github.com/Freaky/Compactor)

### خطوات استخدام Compactor:

1. افتح البرنامج (يفضل كمسؤول).
2. في الإعدادات، اختار **Compression** وخليه **LZX** لأعلى ضغط (لو بروسيسورك ضعيف، اختار خيار تاني أو خليه على الـdefault).
3. دوس **Save**.
4. ارجع لفوق واختار الفولدر اللي عايز تضغطه من **Choose a folder**.

هتشوف 4 ألوان:

- **أبيض:** المساحة اللي وفرتها (صفر لو الفولدر مش مضغوط).
- **أخضر:** مساحة الملفات المضغوطة.
- **أزرق:** الملفات القابلة للضغط. البرنامج عنده hash table بيخليه يعمل skip للملفات المضغوطة زي ملفات الريباك.
- **أصفر/برتقالي:** الملفات اللي مش هتوفر مساحة، فبيستثنيها.

## نصائح لاستخدام Compactor

- شغّل البرنامج كمسؤول.
- لو بروسيسورك قوي، شغّله على فولدرات الألعاب كلها. هيقلل وقت التحميل وهيضغط المساحة (هياخد وقت، فسيبه شغال بالليل).
- فيه جدول بيوضح حجم 5000 لعبة قبل وبعد الضغط: link to be added.
- **متستخدمهوش على:** ملفات الويندوز، فولدر AppData (ممكن يسجل خروجك من المواقع)، Program Files كله مرة واحدة (شغّله على برنامج برنامج).
- **متستخدمهوش على:** الأفلام، الفيديوهات، الصور، الريباك، والملفات المضغوطة (هيتعملها skip تلقائي).
- **استخدمه على:** ألعاب ستيم، برامج أدوبي، برامج الأوفيس، برامج أوتوديسك، برامج توباز، أي برنامج كبير.

## ملحوظات مهمة:

- البرنامج فيه خاصية File Locking، بس أنا مجربتهاش.
- الـProgress bar بيحسب بعدد الملفات، مش بالحجم. لو وقف عند ملف كبير، ده طبيعي. افتح Task Manager وتأكد إن فيه استهلاك للبروسيسور والديسك.
- لو دوست Stop، هيوقف بعد ما يخلص الملف اللي شغال عليه. **ماتعملش End Task** إلا لو مش فارق معاك الملفات.
- في Properties، هتلاقي size (الحجم الأصلي) وsize on disk (الحجم بعد الضغط).
- لو أي ملف اتغير بعد الضغط، هيتفك ضغطه.
- الملفات المضغوطة بخوارزمية معينة مينفعش تضغطها تاني بخوارزمية مختلفة. لازم تفكها الأول.
- الملفات المضغوطة بيتعملها skip تلقائي.

بعد ما تخلص الخطوات دي، المفروض الـ C يفضالك نصها أو أكتر، وفولدرات الألعاب هتلاقي فرق كبير في بعض الألعاب.

ده اللي كان عندي النهاردة، أشوفكم مرة تانية، والسلام عليكم.
