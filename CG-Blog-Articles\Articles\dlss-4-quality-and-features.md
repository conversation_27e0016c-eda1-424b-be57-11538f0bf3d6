كل ما أقول لحد إن DLSS 4 جودته أحسن بكتير، يقول لي الجملة المعتادة: "مش فارقة ما هو حصري للجيل الجديد بس" 🤦‍♂️.

## تقنية DLSS 4 مدعومة في كل كروت RTX

كروت RTX كلها فيها:

- **Tensor Cores:** وهي مجموعة بروسيسورات صغيرة وظيفتها الوحيدة إنها تنفذ عمليات ضرب المصفوفات بسرعات عالية جدًا، وهي العملية الرئيسية في الـ AI.
- **RT Cores:** ودي مجموعة بروسيسورات صغيرة برضه، بس وظيفتها تنفذ عمليات تتبع الأشعة (Ray Tracing).

_الفرق الوحيد بين كل جيل والتاني هو العدد والسرعة ودقة تنفيذ العمليات الحسابية._

**أنا اتكلمت عن تقنية الـ Ray Tracing والـ RT Cores بشكل مفصل قبل كده في كذا مقال، أهمهم المقال ده: link to be added.**

أما بالنسبة لتقنية DLSS فهي بتستخدم الـ **Tensor Cores** في كل عملياتها تقريبًا.

## مصدر سوء الفهم

### أغلب الناس فاكرين إن الـ DLSS تقنية واحدة بإصدارات مختلفة، لكن الـ DLSS كذا تقنية:

- **Super Resolution:** بتشتغل عن طريق إنها تخلي اللعبة ترندر على دقة (resolution) قليل، وهي باستخدام الـ AI تكبر الصورة لحجم الشاشة تاني.
- **Ray Reconstruction:** وده بيتنبأ بنتايج أفضل للـ Ray Tracing عن طريق الـ AI بدل ما يخلي الـ RT Cores تعيد حساباتها كذا مرة.
- **Frame Generation:** ودي تحتها تقنية جديدة اسمها Multi Frame Generation، وهي مسؤولة عن إنشاء فريمات جديدة بالـ AI بالاعتماد على الفريمات اللي مترندرة.

> الـ Super Resolution والـ Ray Reconstruction مدعومين في كل كروت RTX. الـ Frame Generation هو اللي حصري لكروت RTX 40، والـ Multi Frame Generation حصري لكروت RTX 50.

زي ما أنت شايف في الصورة كده:

![DLSS 4 Support](~/Articles/dlss-4-quality-and-features/image1.png)

## إيه الجديد في DLSS 4؟

الـ DLSS 4 ضاف تقنية الـ MFG لكروت RTX 50، لكن برضه نزل بتحديث لكل خصائص الـ DLSS.

الـ Super Resolution بالتحديد اتعمله AI Model جديد مبني بمعمارية الـ Transformer، دي نفس المعمارية اللي اتبني عليها ChatGPT!

الموديل القديم كان معمول بمعمارية CNN، ودي اتكلمت عنها في المقال ده: link to be added لكن للاختصار، هي بتقسم الصورة لأجزاء وتعالج كل جزء لوحده.

بطبيعة الحال، أنت لو هتشوف جزء من الصورة بس مش هتقدر تخمن التفاصيل زي ما هتقدر تخمنها لو أنت شايف الصورة كلها.

وده بالظبط اللي الـ Transformer بيعمله، بيقدر يجمع معلومات عن الفريم كله مرة واحدة، فبالتالي بيقدر ينتج تفاصيل أفضل بكتير.

طب ده يفرق معاك في إيه كجيمر عايز يشغل لعبته بأداء كويس؟

أنت تقدر تشغل الـ DLSS 4 على أي لعبة بتدعم تقنية DLSS عمومًا، وعلى أي كارت من كروت RTX، وكمان بشكل رسمي (مش تعديلات ولا مودات). حتى لو قديمة؟ أيوة، حتى لو قديمة من غير ما تنزلها أي تحديثات!

## إزاي أشغل DLSS 4 عندي؟

الطريقة بسيطة، أول حاجة اتأكد إنك منزل آخر تحديث من تعريفات انفيديا (متجرب على 572.16):

1. افتح **Nvidia App** وروح لـ `Graphics`.
2. المفروض تلاقي لعبتك هنا، بس لو ملقيتهاش دوس على التلات نقط واختار `Add Program`، وضيفها بنفسك.
3. انزل لتحت لغاية `Driver Settings` (لو لقيتها أول حاجة فوق عادي).
4. روح عند `DLSS Override - Model Presets`، ودوس عالخيار اللي قدامها.
5. ممكن تختار `Use Different Settings` لو مثلاً عايز تفعّل الموديل الجديد على الـ **DLSS Super Resolution** من غير الـ **Ray Construction.**
6. عند التقنية اللي عايزها، اختار `Latest`.
7. دوس `Apply`.
8. شغل اللعبة!

![DLSS 4 Override Steps](~/Articles/dlss-4-quality-and-features/image2.jpg)

هتلاحظ الفرق بسرعة جدًا حتى لو قللت الجودة، جرب بنفسك.

حلو، مش كده؟ طب الموديل الجديد ده أكيد أتقل، صح؟ هو آه، فيه ضعف عدد الـ Parameters، فهيقلل الأداء شوية، بس الجودة المُحسنة هتخليك تقدر تقلل الدقة بتاع DLSS لـ Balanced أو Performance وتاخد جودة أفضل بكتير.

![مقارنة الموديل الجديد مع القديم](https://tpucdn.com/review/nvidia-dlss-4-transformers-image-quality/images/geforce-30-cyberpunk-dlss-4-transformer.png)

## التقنية المجهولة RTX VSR

فيه تقنية تانية برضه مشوفتش حد اتكلم عنها خالص، لسه نازل لها تحديث برضه مع DLSS 4 وهي الـ RTX VSR. لو متعرفش التقنية دي، فهي اختصار لـ Video Super Resolution، ممكن تعتبرها DLSS بس للفيديوهات 😅. التقنية دي مدعومة في كل كروت RTX برضه، يعني ممكن تشغل فيديو عاليوتيوب على 720p وهو يرفع الدقة بالـ AI لحجم شاشتك!

**خطوات تفعيلها بسيطة برضه:**

1. روح عند `System`.
2. هتلاقي `RTX Video Enhancements`، دوس عالخيار اللي عند `Super Resolution`.
3. اختار ON، وممكن تعدل في الجودة تخليها أوتوماتيك أو Manual.
4. ممكن تعلم العلامة دي عشان يظهر لك ووترمارك صغير على الفيديو يبين إن الخاصية شغالة _(ممكن تطفيها بعدين)_.
5. اختار OK وشغل أي فيديو على أي متصفح!

![RTX VSR Enable](~/Articles/dlss-4-quality-and-features/image3.jpg)

باختصار، DLSS 4 مش بس للجيل الجديد، بل تقدر تستفيد منه على أي كارت RTX، وكمان فيه تحسينات ملحوظة في جودة الصورة. جرّب بنفسك وشوف الفرق!
