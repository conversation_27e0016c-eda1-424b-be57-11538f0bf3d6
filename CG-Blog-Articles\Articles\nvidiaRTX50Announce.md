# انفيديا RTX 50: نظرة على الجديد والمهم

انفيديا من كام ساعة أعلنت عن كروت RTX 50، في البوست ده هوضحلك شوية حاجات عشان متتغفلش وكمان هشرحلك ايه الجديد وايه اللي المفروض تهتم بيه.
لو معاك أي كارت قديم من كروت RTX ممكن البوست ده يهمك برضه

## DLSS 4: تغيير جذري في الأداء

زي ما "طوب الأرض" كان متوقع، انفيديا عملت Frame Generation عن طريق الـ Extrapolation بدل الـ Interpolation.

**الطريقة القديمة:**

بترندر الفريم الحالي وليكن فريم رقم 1 وتعرضه على الشاشة (كدة فريم 1 معروض خلي بالك)، ثم ترندر الفريم الجاي اللي هو رقم 2 من غير ما يتعرض على الشاشة، وبعدين تعمل فريم بينهم يبقى رقمه 1.5 مثلاً، ثم تعرض الفريم 1.5 اللي هي عملته ده، ثم تعرض الفريم 2، وتستمر العملية دي على طول.

- **المشكلة:** ده كان بيعمل مشكلة واضحة جداً، إن استجابة اللعبة بتبقى بطيئة/تقيلة حتى لو شكلها smooth، وده عشان التقنية محتاجة تأخرلك عرض الشاشة عقبال ما تعمل فريمات وسيطة.

**الطريقة الجديدة:**

إن التقنية دي بقيت بترندر وتعرض فريم 1 وترندر وتعرض فريم 2 عادي جداً
ثم تنتج فريم 3 عن طريق التنبؤ بيه!
يعني من غير ما تعمل حسابات الإضاءة من تاني ولا تعمل حسابات الفيزياء ولا أي حاجة
مجرد تشوف آخر فريمين وتتوقع التالت شكله عامل ازاي (دي حاجة بديهية أوي يعني معرفش إيه اللي أخرهم كدة بصراحة)

---

كروت RTX 50 كلها هتقدر تنتج عدد كبير من الفريمات عن طريق الـ Frame Generation (انفيديا بتقول 3 إنتاج و1 حساب لكل 4 فريمات معروضة عالشاشة)

انفيديا بتقول إنهم عملوا تعديل في الهاردوير بتاع الـ FG عشان يخلوه يقدر ينتج فريمات كتيرة بحسبة واحدة بس، وانهم استبدلوا وحدة حساب الموشن فيكتورز بموديل AI يشتغل على الـ tensor cores.

**RTX 40:**

كروت RTX 40 كسبت نفس الميزة برضه بس هتقدر تنتج فريم واحد بس من المستقبل مش تلاتة. انفيديا بتقول انهم حاولوا يخلوه ينتج أكتر من فريم لكن الهاردوير بتاعه محتاج يعمل حسبة الموشن فيكتورز كل مرة ينتج فيها فريم جديد عشان كدة لو زودوا عدد الفريمات الآداء هيبقى أقل من لما تطفي الخاصية خالص.

**RTX 30/20:**

كروت 30 و 20 ملهومش حاجة من الـ Frame Generation. بس خاصية DLSS كاملة على كل الكروت هتستلم موديلات جديدة هتشتغل على أي لعبة فيها DLSS.

**DLSS الجديد:** الموديل الجديد معمول بـ Transformer Architecture بدل الـ CNN. أيوة ترانسفورمر زي شات جيبيتي كدة. ميزته انه بيقدر يشوف الصورة كلها ويربط التفاصيل ببعض، على عكس الـ CNN اللي بيعالج كل جزء من الصورة لوحده. جودته المفروض تكون أفضل بكتير جداً من الأول والمفروض يخلصنا من الفازلين اللي الألعاب بتدهن بيه الشاشة اليومين دول. كمان هيبقى عندك القدرة تستبدل الـ DLSS القديم بالجديد عن طريق أبلكيشن انفيديا في أي فيها DLSS.

---

## الأداء الحقيقي: هل RTX 5070 = RTX 4090؟

نيجي بقى للمهم أوي، انفيديا بتقول ان الـ RTX 5070 هيكون بسعر 550 دولار بس هيديك آداء قد الـ 4090! ده كلام مضلل شوية، هو هيديك الآداء ده فعلاً لكن بالـ DLSS FG عن طريق انه يعمل 3 فريمات أو أكتر من الهوا ويرندر فريم واحد حقيقي. تقدر تتأكد من الكلام ده عن طريق الجراف اللي نشروه. شوف المقارنة بين الـ 5090 والـ 4090، هتلاقي الزيادة في لعبة Far Cry 6 اللي مطفي فيها الـ DLSS تقدر بحوالي 20% بس. كذلك في كل الكروت هتلاقي الزيادة بين الجيل اللي فات والجديد من 20% لـ 30% بس.

## أداء الـ AI: تحسينات مع بعض التحفظات

في الـ AI القصة مختلفتش كتير. لو بصيت عالجراف هتلاقي آداء Flux أعلى بـ 2X من الجيل اللي فات! لكن ده كلام مضلل برضه لإنك لو بصيت تحت على الفوت برنت هتلاقي إن الموديل شغالة بنصف دقة الحسابات على الـ RTX 5090 اللي هي fp4، بدل fp8 على الـ 4090. ده هيفرق جداً في الآداء واستهلاك الرام وكمية الحسابات وكل حاجة، طبعاً هيفرق في النتيجة كمان فبالتالي مش هيديك نفس تفاصيل الصور ولا قريب منها حتى.

## VRAM و Reflex 2: لمحات سريعة

الـ VRAM نكتة هنسيبها ليوم تاني بس هقول كلمة صغيرة، انفيديا عملت حتة موديل صغنون وظيفته يضغط الداتا بتاعت اللعبة في الـ vram، المبهر إن الموديل ده عنده القدرة يعالج الداتا دي وهي مضغوطة! دي حاجة أول مرة نشوفها عموماً بس لازم نشوف نتايجها بعدين قبل ما نقول حاجة.

تفصيلة إضافية تانية، بيقولوا شغالين على تقنية Reflex 2 Frame warp، مفيش تفاصيل عنها بس الظاهر انها هتغير الفريم بعد ما يترندر عشان يديك آخر حركة بعتها الماوس قبل ما الفريم يوصل للشاشة.

## الخلاصة

عموماً أنا كنت متوقع حاجات معينة مبهرة أكتر من كدة بس شكلها لسة في المطبخ ومحتاجة تستوي شوية كمان. لو حد مش فاهم حاجة الكومنتات مفتوحة خد راحتك واسأل. والسلام عليكم
