خبر في الإنجاز عشان محدش حس بيه:

انفيديا من كام أسبوع نزلت السورس كود بتاع RTX Remix. البرنامج ده هو اللي معمول بيه Portal with RTX، ووظيفته إنه ياخد الألعاب القديمة ويقطع عملية رندرة الجرافيكس من النص ويوديها على مسار جديد، تقدر فيه تضيف للعبة ray tracing أو DLSS أو تغير الـ materials بتاعت اللعبة أو المجسمات أو تعمل أي حاجة تقريباً، وكله بيترندر ضمن اللعبة الأصلية بنفس اللوجيك بتاعها.

## آلية العمل

ده بيتعمل عن طريق إنك بتشغل اللعبة عادي، وبتاخد زي سكرين شوت. زرار بتدوس عليه بيخزن كل الحاجات اللي حواليك سواء مجسمات أو مصادر ضوء أو materials أو shaders، أي حاجة موجودة في الـ VRAM بتتلقط وتتخزن، ويتخزن مكانها الأصلي من اللعبة والملف اللي جت منه.

بعدها بتفتح أي برنامج تعديل جرافيكس بيدعم صيغة الجرافيك USD، وتاخد فيه الملفات دي وتعدل فيها بمزاجك. قول هتغير كل حاجة فيه وتعمله من تاني، عادي.

وبعد كده بتشغل اللعبة بالـ bridge بتاع انفيديا، وهو بيحمل الملفات التانية دي ويحطها مكان المجسمات والإضاءات والـ materials الأصلية. وبكده اللعبة يبقى شكلها جديد بتلمع وهي قديمة من غير ما المحرك ياخد حمل إضافي.

## الألعاب المدعومة

حالياً الناس بيجربوا الموضوع ده على ألعاب كتيرة، منها GTA VC وSA وV.

طبعاً الموضوع مش سهل ومحتاج شغل كتير عشان يطلع نتيجة حلوة، بس الفكرة إن دلوقتي أي حد يقدر يعدل جرافيك أي لعبة!

البرنامج لسه في مرحلة تجريبية أوي، بس هو مفتوح المصدر، فلو ليك في السكة دي روح لـ github/NVIDIAGameWorks/rtx-remix.
