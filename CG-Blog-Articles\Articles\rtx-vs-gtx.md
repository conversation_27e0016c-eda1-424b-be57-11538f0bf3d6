بمناسبة إني شايف كروت GTX لسة لغاية النهارده بتتباع بـ 7 و 8 آلاف مستعملة.

الـ 1000 ولا الـ 2000 جنيه الزيادة اللي هتصرفهم عشان تجيب RTX 3050، اللي هو أقل كارت RTX، هيديك:

- تقنيات DLSS، اللي جودتها أعلى بكتير من FSR (مفيش مقارنة أصلاً، بل أحيانًا DLSS بتكون أحسن من الـ Native).
- تقنية VSR اللي بتعلي جودة الفيديوهات بالـ AI لدرجة هتخلي اللي جنبك يستغرب إزاي مشغل اليوتيوب على 720p ومطلع الجودة دي.
- دعم هاردوير للـ Ray Tracing اللي بقى مطلوب في بعض الألعاب اليومين دول، وحتى لو مش هتستعمله في الجيمز هيفيدك في برامج الـ 3D زي بلندر وثري دي ماكس.
- 8 جيجا VRAM، دول مطلوبين في بعض الجيمز دلوقتي، وفوق كده هتقدر تستفيد جامد جدًا بيهم في الشغل.
- محرك ميديا أفضل بيدعم تشفير HEVC وفك تشفير AV1 (التقنيات دي بتديك فيديوهات جودتها أعلى بحجم أقل).
- مسرعات AI اللي هي الـ Tensor Cores، ودي في العصر الحالي تقدر تستفيد بيها في تشغيل برامج إنتاج الصور شبيهة بـ Midjourney وتشغيل برامج LLM شبيهة بـ ChatGPT بشكل محلي على جهازك (يعني من غير نت خالص!!).
- دعم للـ Mesh Shaders اللي لعبة Alan Wake 2 طلبتها، وقدام شوية فيه ألعاب كتير هتطلبها.
- تقنية الـ ReBAR اللي بتزود حجم الداتا اللي بتتبعت بين البروسيسور والكارت.

صحيح ده كله ممكن يديك أداء مباشر (raw power) أقل من كارت GTX أرخص منه في السعر، بس الموضوع عامل زي الفرق بين النجار اللي بيشتغل بأدوات تقليدية والنجار اللي معاه مصانع وماكينات، الاتنين هيعملولك نفس الحاجة مع وجود فروقات بسيطة.

حتى لو الأشطر فيهم هو اللي شغال بالأدوات التقليدية، اللي هيخلص الشغلانة أسرع هو اللي معاه ماكينات ومصانع (وده هنا هو كروت RTX).

لو لسة معاك GTX، انبسط بيه عادي، الكروت لسة ممتازة وشغالة كويس جدًا. بس لو هتحدّث أو هتجيب كارت جديد عمومًا، خد بالك من النقط اللي قولتها دي. لو لقيت الكارت غالي، يبقى روح لكروت AMD، بس ابعد عن كروت GTX.
