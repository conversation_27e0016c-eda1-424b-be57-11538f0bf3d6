## Step-by-Step Guide to Setting Up the Project

### Step 1: SSH to Server by IP and Password

To connect to your server, you will need to use SSH. Open a terminal or command prompt on your Windows machine and use the following command, replacing `your_server_ip` with the actual IP address of your server and `your_password` with your actual password:

```bash
ssh user@your_server_ip
```

When prompted, enter your password: `your_password`.

### Step 2: Copy a Zip File to the Server

To copy the `drive_monitor-master.zip` file from your Windows desktop to the server, use the `scp` command in a new terminal or command prompt on your Windows machine:

```bash
scp ~/Desktop/drive_monitor-master.zip user@your_server_ip:~
```

### Step 3: Unzip the Project in the User Home Folder

After copying the file, SSH into your server if you haven't already, and navigate to your home directory:

```bash
cd ~
```

Install unzip:

```bash
sudo apt install unzip
```

You will need to enter your server password, **note that it will not appear as you type**.
Unzip the project:

```bash
unzip drive_monitor-master.zip
```

### Step 4: CD into the Folder `drive_monitor-master`

Navigate into the unzipped project folder:

```bash
cd drive_monitor-master
```

### Step 5: Install Pip, Requirements, Node, NPM, and Mudslide

Install `pip` for Python3. Since `pip` doesn't come pre-installed, you'll need to use the following command to install it:

```bash
sudo apt update
sudo apt install python3-pip
```

Verify `pip` installation:

```bash
python3 -m pip --version
```

Install the requirements specified in `requirements.txt`:

```bash
python3 -m pip install -r requirements.txt
```

Install Node.js and npm.

```bash
sudo apt install nodejs npm
```

Ensure you have the latest versions:

```bash
sudo npm cache clean -f
sudo npm install -g n
sudo n stable
```

Install `mudslide` package globally:

```bash
sudo npm install -g mudslide
```

### Step 6: Test Mudslide Package

Test the `mudslide` package:

```bash
mudslide me
```

The expected output:

```
Created mudslide cache folder: [some path]
× error     Not logged in
```

If it hangs on `cache folder: [some path]`, and never shows `Not logged in`, delete that folder and try again:

```bash
rm -rf "[some path]"
mudslide me
```

This indicates that `mudslide` is installed correctly.

### Step 7: Run the Python Project for the First Time

Ensure you are in the `drive_monitor-master` directory:

```bash
cd ~/drive_monitor-master
```

Run the `main.py` file:

```bash
python3 main.py
```

The script will prompt you to enter:

- Username
- Password
- Confirm Password

These credentials will be used to log in to the web interface. After entering the required information, the script will create a `config.json` file and exit.

### Step 8: Edit the config.json File

Open the `config.json` file in a text editor:

```bash
nano config.json
```

The file will look something like this:

```json
{
  "folder_id": "YOUR_FOLDER_ID_HERE",
  "check_interval": 60,
  "credentials_file": "credentials.json",
  "max_send_attempts": 3,
  "downloads_dir": "downloaded_files",
  "processed_files_store": "processed_files.json",
  "queue_file": "file_queue.json",
  "token_file": "token.pickle",
  "enable_startup_cleanup": true,
  "cleanup_max_age_days": 7,
  "phone_numbers": [],
  "email_alerts": {
    "enabled": false,
    "sender_email": "<EMAIL>",
    "recipient_email": "<EMAIL>",
    "error_cooldown": 1800,
    "Resend_API_KEY": "YOUR_RESEND_API_KEY_HERE"
  },
  "web_server": {
    "host": "0.0.0.0",
    "port": 5000,
    "debug": false
  },
  "flask_secret_key": "GENERATE_SECURE_SECRET_KEY_HERE",
  "NGROK_AUTHTOKEN": "YOUR_NGROK_AUTHTOKEN_HERE",
  "NGROK_DOMAIN": "YOUR_NGROK_DOMAIN_HERE"
}
```

Update the `NGROK_AUTHTOKEN` and `NGROK_DOMAIN` fields with your Ngrok authentication token and domain, respectively.

### Step 9: Generate a Flask Secure Key

To generate a secure secret key for Flask, run the following command in a new terminal or your local terminal:

```bash
python3 -c 'import secrets; print(secrets.token_urlsafe(16))'
```

Copy the generated key and update the `flask_secret_key` field in the `config.json` file.

Example:

```json
"flask_secret_key": "your_generated_secret_key_here"
```

Save and close the file.

### Step 10: Verify the Web Interface

After completing the previous steps, run the `main.py` file again:

```bash
python3 main.py
```

Look for the URL in the logs appearing on your screen (or go directly to the domain if you configured it). Open a web browser and navigate to the URL. Login to the web interface using the credentials you provided earlier. Verify that everything is working as expected.

### Step 11: Exit the Script and Run the Server Management Script

After verifying the web interface, press `Ctrl+C` to exit the `main.py` script.

To run the server in the background, you need to make the `manage_server.sh` script executable:

```bash
chmod +x manage_server.sh
```

Then, run the script using the following command:

```bash
./manage_server.sh
```

This script will run a task in the background with the server on. You can now proceed to use the product.

Note: The `manage_server.sh` script is located in the `drive_monitor-master` directory. Make sure you are in the correct directory before running the script. If you are not in the `drive_monitor-master` directory, navigate to it using the `cd` command:

```bash
cd ~/drive_monitor-master
```

Then, you can run the script using the command above.

By following these steps, you should now have the project set up and running in the background. You can access the web interface using the URL or domain you configured earlier.
