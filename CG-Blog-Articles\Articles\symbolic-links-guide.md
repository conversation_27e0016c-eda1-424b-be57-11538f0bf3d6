فيه ميزة مفيدة جداً في أغلب أنظمة الملفات مفيش كتير يعرفوها اسمها **symbolic links**. الخاصية دي عبارة عن بورتال بيشاور على مكان ملف من مكان تاني. حاجة عاملة زي الـShortcut لكن بتشتغل مع البرامج، الألعاب، والويندوز بشكل عام.

أنا استخدمت الخاصية دي عشان ألعب **Starfield** من غير stutters كل شوية. اللعبة حجمها كبير ومفيش مكان يكفيها في أي SSD واحد عندي، فقررت أقسم اللعبة على هاردين مختلفين واتنين SSD مختلفين. النتيجة؟ اللعبة كانت شغالة وبـ load من 4 أجهزة تخزين مختلفة!

---

## الفرق بين الـSymbolic Link و Hard Link

### 1. **Symbolic Link (Soft Link):**

- بيشتغل في طبقة السوفتوير.
- بيعمل ملف يشاور على ملف تاني، والملف التاني ده بيشاور على مكان الداتا الفيزيائي في الهارد.

### 2. **Hard Link:**

- مدعوم من كل أنظمة التشغيل تقريباً (القديمة خصوصاً).
- مدعوم في نظام ملفات Fat32.
- بيعمل ملف يشاور على مكان الداتا الفيزيائي مباشرةً.

---

## استخدامات Symbolic Links

اللينكات دي بتفتح لك باب لاستخدامات متعددة جدًا، زي:

- **مشاركة الملفات بين البرامج:** لو عندك ملف كبير وبرنامجين مختلفين بيستخدموه، ممكن تعمل لينك للبرنامج التاني بدل ما تنسخ الملف وياخد مساحة زيادة.
- **تخزين عبر أجهزة متعددة:** زي حالتي، لو عندك لعبة أو ملف كبير جدًا، ممكن تنقل جزء منه لهارد آخر وتعمل لينك منه للموقع الأصلي.
- **نقل البرامج والفولدرات:** ممكن تنقل فولدر برنامج من قرص الـ C لأي مكان تاني وترجع تعمل لينك للمكان الأصلي في قرص الـ C، والبرنامج هيشتغل كأن مفيش أي تغيير!

---

## تنبيهات مهمة عند استخدام Symbolic Links

- **عدم استخدام السوفت لينك مع ألعاب Anti-cheat:** السوفت لينك بيشتغل من الكيرنال، ومع ألعاب الـAnti-cheat، ممكن تظهر مشاكل.
- **تجنب نقل ملفات النظام أو فولدرات حساسة:** زي فولدر اليوزر، Downloads، أو temp، لأن مشاكل كتير حصلت لما اتنقلت الملفات دي.
- **جرب الأول على ملف غير مهم:** مثلاً جرب تمسح اللينك وتحقق إذا الملف الأساسي مازال موجود أم لا قبل ما تجرب على ملفاتك الحقيقية.

---

## الطريقة بكل بساطة:

### خطوات إنشاء Symbolic Link باستعمال برنامج Link Shell Extension:

1. ابحث عن البرنامج واسمه: **Link Shell Extension (LSE)**، حمّله وقم بتثبيته.
2. اضغط كليك يمين على أي ملف أو فولدر عايز تعمله لينك.
3. اختار **Pick Link Source**.
4. اتجه للمكان اللي عايز تحط اللينك فيه (تخيل نفسك بتلعب لعبة بورتال!).
5. اضغط كليك يمين في مكان فارغ واختر **Drop As** ثم حدد **Symbolic Link**.
6. **استمتع!** استخدم الخاصية بحرية في تحسين أداء جهازك وتسريع الوصول لملفاتك.

---

### ملاحظة أخيرة لأصحاب Windows 11:

لو مش لاقي الخيارات اللي ذكرتها في كليك يمين، تأكد إنك اخترت **Show more options** للحصول على الخيارات الكاملة.
