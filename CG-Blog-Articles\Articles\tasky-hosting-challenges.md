من المشاريع اللي كنت شغال عليها من فترة (بهدف التعلم) هو **Tasky**. برنامج to-do list عادي وبسيط، خش الزق يوزرنيم وباسوورد وحط تاسكاتك واوصلّها من أي جهاز. 😁

وزي أي حاجة اليومين دول، كان لازم أضيف حاجة AI كده، شوف الفيديو: [tasky-video.mp4](https://cg-blog-articles.pages.dev/Articles/tasky-video.mp4)

## تحديات بناء تطبيق مجاني بالكامل

البرنامج ده يبدو من برا بسيط، مجرد to-do list، ده أول مشروع أي حد بيعمله في الحياة. بس عشان أقدر أوفره للكل وبشكل مجاني، احتجت:

### 1. مكان للفرونت إند (الواجهة الأمامية)

دي سهلة، عندك Vercel و GitHub Pages و Cloudflare Pages. أنا اخترت الأخير من غير سبب محدد.

### 2. مكان للباك إند (الواجهة الخلفية)

دي صعبة حبتين، الـ build اللي عملته بيخرج Docker image، وده عشان تلاقي حد يستضيفه ببلاش محتاج تمـ/وت مرتين 😂.

بعد بحث كتير، لقيت راجل طيب اسمه Koyeb عامل "Generous Free Tier" هوست على نص جيجا رام بحالهم 🤣. أحسن من بلاش، ده برضه سبب إنك لو دخلت عليه هتلاقيه واخد كام ثانية بيحمل.

### 3. مكان للداتابيز (قاعدة البيانات)

الداتابيز اللي استخدمتها بطبيعة الحال هي PostgreSQL، كانت سهلة نسبيًا. دورت مرتين تلاتة لقيت Neon عندهم هوست مجاني لداتابيز صغيرة حطيتها عليه ووصلتها بالباك إند وعملت الـ migrations.

### 4. موفر للموديل الذكاء الاصطناعي

وأخيرًا، موفر للموديل اللي بياخد الرسايل ويحولها تاسكات. سهلة نسبيًا عشان الشركات كلها واخدة التريند وقاعدين بيحرقوا فلوس، بس المشكلة في الـ scaling. أي خدمة بشوفها بلاقي موديل عبقري بس يكفي استخدام دوري لشخص واحد بس، اتنين بالكتير، ورغم إن مفيش حد استخدم البرنامج ولا حتى الشخصين دول 😂، بس كنت لازم أعمل حسابي.

في الآخر، اخترت Cloudflare Workers عشان مديني عدد طلبات كبير لكل دقيقة حتى لو كان الموديل مش ذكي، بس طالما كويس كفاية.

## كيف يعمل الـ AI في Tasky؟

الفرونت إند (جهازك) بيبعت الرسالة للباك إند، وهو بيعمل الـ request لكلاود فلير ويديك شوية تاسكات. جهازك بيبعت التاسكات للباك إند تاني كإنك ضايفهم بإيدك 🤡.

بس فيه سبب وجيه هسيبك تتوقعه، ودي مش أسرار عسكرية يعني، البرنامج كله أوبن سورس. 😅

## من الفكرة إلى المنتج: ما وراء الكواليس

شوف أنت لو حبيت تعمل البرنامج ده على جهازك مش هياخد معاك يوم على بعضه، بس عشان تخليه منتج في الـ production هتحتاج أسابيع وتعمل error handling وتتأكد إن محدش هيدخل كلمة مسيئة مكان اليوزر نيم مثلاً، أو تعمل حسابك على الـ latency الناتجة من وجود 3 خدمات مختلفة بتخدم برنامج واحد.

تقدر تجربه بنفسك من هنا: https://tasky.creative-geek.tech

دمتم بخير.
