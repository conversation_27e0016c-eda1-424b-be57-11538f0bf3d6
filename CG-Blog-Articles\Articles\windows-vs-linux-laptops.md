أذكر لما واحد من صحابي شافني بفتح الماك بوك وشاف شعار آبل منور على خلفية سودا وسألني هو إيه ده؟ قولتلّه كان معموله shutdown، قام باصصلي كده --> 💀. وقالي: ماك إيه اللي تعمله shutdown! مش بيتعمله كده ده، ده بتقفل شاشته وتحطه في شنطتك.

والحقيقة إن كل اللابات ينفع تعمل كده عادي .... طالما مش مستخدم نظام ويندوز 🫠.

## ويندوز: مشكلة الاعتمادية والأداء

ويندوز رغم دعمه لكمية كبيرة من البرامج والألعاب هو أسوأ نظام موجود حاليًا من حيث الاعتمادية والأداء. يا راجل ده لينيكس عشان يشغل ألعاب ويندوز بياخد كل الأوامر ويترجمها عالطاير، وعارف رغم كل الخطوات الزيادة دي؟ بيشغل اللعبة أسرع من ويندوز!!!!

مش خيال علمي، ده اللي حصل الفترة اللي فاتت فعلاً عن طريق Steam OS، وهو سيستيم مبني على لينيكس (Arch btw 😂) من تطوير فالف (ستيم) تقدر النهارده تسطّبه على أي جهاز.

## تجربة واقعية: ويندوز مقابل لينيكس

نرجع لموضوعنا، جرب (أو متجربش بس تخيل إنك بتجرب يعني) تشغل كام برنامج تقيل كده أو لعبة على لابك اللي شغال بويندوز وتقفل شاشة اللاب وتحطه في شنطتك وتصحي الصبح تشغله. لو حصلت معجزة هتطلعه من الشنطة تلاقيه سخن بس هيفتح وبطاريته هتكون نازلة حبة حلوين. بس الراجح إنه هيكون فاصل شحن خالص.

وأنت نايم مايكروسوفت كانت بتحمل أبديت لأوفيس وويندوز وبتبعت داتا باستمرار، ده لو أنت محظوظ وعمل sleep أصلاً 🤡.

الموضوع ده خلاني أتساءل، هل لو سطبت لينيكس على اللاب اللي ببروسيسور جيل تاني ده، هعرف أستعمله لاب توب تاني؟

الإجابة هي آه. من المضحكات في الموضوع إني لما فتحت ويندوز اللاب كان مشغل المروحة على أعلى سرعة، كنت حاسس إني قاعد في المطار واللاب بيجهز نفسه عشان يطير، لكن أول ما دخلت على لينيكس المروحة وقفت واللاب بقى بارد عادي 😅. سطبته وقفلت الشاشة زي ما بعمل مع الماك بالظبط، صحيت تاني يوم كان بارد وبطاريته نزلت 10% بس (اللاب جيل تاني متنساش).

## الخاتمة: مستقبل لينيكس

فالحقيقة التحويل للينيكس بيبقى اختيار أحسن كل يوم عن التاني. ولغاية ما يحصل حاجة تخلي الشركات تزود من دعم لينيكس (زي مجهودات فالف في تطوير النظام)، أديني مستني السنة بتاعته: "the year of Linux desktop".
