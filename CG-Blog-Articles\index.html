<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Creative Geek Blog Articles</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        margin: 0;
        background-color: #f5f5f5;
      }
      .container {
        text-align: center;
        max-width: 800px;
        padding: 20px;
      }
      h1 {
        color: #333;
        margin-bottom: 30px;
      }
      .articles-list {
        list-style: none;
        padding: 0;
      }
      .articles-list li {
        margin: 15px 0;
      }
      .articles-list a {
        color: #2c3e50;
        text-decoration: none;
        font-size: 1.2em;
        transition: color 0.3s ease;
      }
      .articles-list a:hover {
        color: #3498db;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>مقالات Creative Geek</h1>
      <ul class="articles-list" id="articlesList">
        <!-- Articles will be loaded here -->
      </ul>
      <h2>Pages</h2>
      <ul class="articles-list">
        <li><a href="Pages/about.md">About</a></li>
      </ul>
    </div>

    <script>
      async function loadArticles() {
        const articlesList = document.getElementById("articlesList");

        try {
          // First fetch the index.json which contains the list of articles
          const indexResponse = await fetch("Articles/index.json");
          const articlesIndex = await indexResponse.json();

          // For each article in the index, fetch its details
          for (const article of articlesIndex) {
            const articleResponse = await fetch(
              `Articles/${article.name}.json`
            );
            const articleData = await articleResponse.json();

            // Skip hidden articles
            if (articleData.hidden) continue;

            const li = document.createElement("li");
            const a = document.createElement("a");

            // Set link to the article's markdown file instead of JSON
            a.href = `Articles/${article.name}.md`;
            a.textContent = articleData.title;

            li.appendChild(a);
            articlesList.appendChild(li);
          }
        } catch (error) {
          console.error("Error loading articles:", error);
        }
      }

      loadArticles();
    </script>
  </body>
</html>
