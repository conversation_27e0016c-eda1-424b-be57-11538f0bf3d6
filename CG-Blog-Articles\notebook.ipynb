{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Server started on port 8000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["192.168.1.30 - - [06/Mar/2025 16:23:31] \"GET / HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:31] \"GET /Articles/index.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:31] \"GET /Articles/fontPreviewer.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:31] code 404, message File not found\n", "192.168.1.30 - - [06/Mar/2025 16:23:31] \"GET /favicon.ico HTTP/1.1\" 404 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:31] \"GET /Articles/nvidiaRTX50Announce.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:31] \"GET /Articles/nomacs-image-viewer.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:31] \"GET /Articles/symbolic-links-guide.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:31] \"GET /Articles/compactor-guide.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:31] \"GET /Articles/av1-hevc-comparison.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Pages/home.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/index.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Pages/profile.jpg HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Pages/cover.jpg HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/index.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] code 404, message File not found\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/cg-blog.json HTTP/1.1\" 404 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] code 404, message File not found\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/time-estimator.json HTTP/1.1\" 404 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] code 404, message File not found\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/platformer-test.json HTTP/1.1\" 404 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/dlss-4-quality-and-features.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/ds4windows-guide.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/material-you-critique.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/fontPreviewer.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/arabic-handwriting-ocr.jpg HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/dlss-4.jpg HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/ds4windows-guide.jpg HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/material-you-critique.jpg HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/nvidiaRTX50Announce.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/nomacs-image-viewer.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/symbolic-links-guide.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/compactor-guide.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:34] \"GET /Articles/av1-hevc-comparison.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:36] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:36] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:43] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:43] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:43] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:23:43] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:24:28] \"GET /Pages/home.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:24:28] \"GET /Articles/index.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:28] \"GET /Pages/profile.jpg HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:28] \"GET /Pages/cover.jpg HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/index.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] code 404, message File not found\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/cg-blog.json HTTP/1.1\" 404 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] code 404, message File not found\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/time-estimator.json HTTP/1.1\" 404 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] code 404, message File not found\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/platformer-test.json HTTP/1.1\" 404 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/dlss-4-quality-and-features.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/ds4windows-guide.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/material-you-critique.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/fontPreviewer.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/arabic-handwriting-ocr.jpg HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/dlss-4.jpg HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/ds4windows-guide.jpg HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/material-you-critique.jpg HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/nvidiaRTX50Announce.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/nomacs-image-viewer.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/symbolic-links-guide.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:29] \"GET /Articles/compactor-guide.json HTTP/1.1\" 200 -\n", "192.168.1.6 - - [06/Mar/2025 16:24:30] \"GET /Articles/av1-hevc-comparison.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:24:33] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:24:33] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:24:34] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:24:34] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:25:13] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:25:13] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:25:58] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:25:58] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:26:51] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:26:51] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:27:01] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:27:01] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:27:23] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:27:23] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:27:37] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:27:37] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:28:05] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:28:05] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:28:19] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:28:19] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:28:30] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:28:30] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:28:37] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:28:37] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:29:09] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:29:09] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:31:20] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:31:20] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:31:37] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:31:37] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:31:43] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:31:43] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:38:14] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:38:14] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:38:22] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:38:22] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:40:36] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:40:36] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:40:57] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:40:57] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:42:06] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:42:06] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:42:42] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:42:42] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:42:52] \"GET /Articles/arabic-handwriting-ocr.md HTTP/1.1\" 200 -\n", "192.168.1.30 - - [06/Mar/2025 16:42:52] \"GET /Articles/arabic-handwriting-ocr.json HTTP/1.1\" 200 -\n"]}], "source": ["from http.server import HTTPServer, SimpleHTTPRequestHandler\n", "import sys\n", "\n", "class CORSRequestHandler(SimpleHTTPRequestHandler):\n", "    def end_headers(self):\n", "        self.send_header('Access-Control-Allow-Origin', '*')\n", "        self.send_header('Access-Control-Allow-Methods', '*')\n", "        self.send_header('Access-Control-Allow-Headers', '*')\n", "        super().end_headers()\n", "\n", "    def do_OPTIONS(self):\n", "        self.send_response(200)\n", "        self.end_headers()\n", "\n", "\n", "server = HTTPServer(('0.0.0.0', PORT), CORSRequestHandler)\n", "print(f\"Server started on port {PORT}\")\n", "try:\n", "    server.serve_forever()\n", "except KeyboardInterrupt:\n", "    server.server_close()\n", "    print(\"\\nServer stopped.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}}, "nbformat": 4, "nbformat_minor": 2}